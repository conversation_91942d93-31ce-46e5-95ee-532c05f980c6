"use client";

import { motion, AnimatePresence } from "framer-motion";
import React, { useState } from "react";

export function Gallery1({ 
  images = [], 
  title = "Gallery", 
  className = "",
  columns = "md:columns-3",
  showTitle = true,
  titleClassName = "",
  containerClassName = "",
  imageClassName = "",
  modalClassName = ""
}) {
  const [selectedImage, setSelectedImage] = useState(null);

  const openModal = (image) => {
    setSelectedImage(image);
  };

  const closeModal = () => {
    setSelectedImage(null);
  };

  // If no images provided, don't render anything
  if (!images || images.length === 0) {
    return null;
  }

  return (
    <section 
      id="gallery" 
      className={`px-[5%] py-16 md:py-24 lg:py-28 bg-background-primary ${className}`}
    >
      <div className={`container ${containerClassName}`}>
        {showTitle && (
          <motion.div
            className="mb-12 text-center md:mb-18 lg:mb-20"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className={`mb-5 text-5xl font-semibold md:mb-6 md:text-7xl lg:text-8xl ${titleClassName}`}>
              {title}
            </h2>
          </motion.div>
        )}
        <motion.div
          className={`gap-8 space-y-8 ${columns}`}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.1 }}
          variants={{
            hidden: {},
            visible: {
              transition: {
                staggerChildren: 0.1
              }
            }
          }}
        >
          {images.map((image, index) => (
            <motion.button
              key={index}
              onClick={() => openModal(image)}
              className="block w-full cursor-pointer overflow-hidden rounded-lg"
              variants={{
                hidden: { opacity: 0, y: 20 },
                visible: { opacity: 1, y: 0, transition: { duration: 0.5 } }
              }}
              whileHover={{
                scale: 1.02,
                transition: { duration: 0.3, ease: "easeInOut" }
              }}
              whileTap={{ scale: 0.98 }}
            >
              <img
                src={image.src}
                alt={image.alt}
                className={`size-full rounded-image object-cover rounded-lg ${imageClassName}`}
              />
            </motion.button>
          ))}
        </motion.div>
      </div>

      {/* Modal */}
      <AnimatePresence>
        {selectedImage && (
          <motion.div
            className={`fixed inset-0 z-[9999] flex items-center justify-center bg-black/80 backdrop-blur-sm p-2 sm:p-4 ${modalClassName}`}
            onClick={closeModal}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
          >
            <motion.div
              className="relative flex items-center justify-center max-h-[80vh] max-w-[80vw] sm:max-h-[75vh] sm:max-w-[75vw] md:max-h-[70vh] md:max-w-[70vw] lg:max-h-[65vh] lg:max-w-[65vw]"
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.8, opacity: 0 }}
              transition={{ duration: 0.3 }}
            >
            {/* Image container with enhanced styling */}
            <div className="relative p-2 sm:p-4 md:p-6 lg:p-8">
              {/* Close button positioned relative to image */}
              <button
                onClick={closeModal}
                className="absolute -top-1 -right-1 sm:-top-2 sm:-right-2 z-10 flex h-8 w-8 sm:h-10 sm:w-10 items-center justify-center rounded-full bg-white/10 text-white backdrop-blur-md transition-all duration-200 hover:bg-white/20"
                aria-label="Close modal"
              >
                <svg
                  className="h-4 w-4 sm:h-6 sm:w-6"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>

              <img
                src={selectedImage.src}
                alt={selectedImage.alt}
                className="max-h-full max-w-full object-contain rounded-lg sm:rounded-xl"
                onClick={(e) => e.stopPropagation()}
              />
            </div>
          </motion.div>
        </motion.div>
        )}
      </AnimatePresence>
    </section>
  );
}
